{"inputs": [{"type": "promptString", "id": "socket_api_key", "description": "Socket API Key", "password": true}], "servers": {"socket-mcp-direct": {"type": "stdio", "command": "node", "args": ["./socket-mcp/build/index.js"], "env": {"SOCKET_API_KEY": "${input:socket_api_key}"}}, "socket-mcp-npx": {"type": "stdio", "command": "npx", "args": ["--yes", "--package", "./socket-mcp", "socket-mcp"], "env": {"SOCKET_API_KEY": "${input:socket_api_key}"}}}}