#!/usr/bin/env node

// Simple test script to verify Socket MCP server can start
import { spawn } from 'child_process';

console.log('Testing Socket MCP server startup...');

// Test with a dummy API key
const env = {
    ...process.env,
    SOCKET_API_KEY: 'test-key-for-startup-test'
};

const child = spawn('node', ['./socket-mcp/build/index.js'], {
    env,
    stdio: ['pipe', 'pipe', 'pipe']
});

let output = '';
let errorOutput = '';

child.stdout.on('data', (data) => {
    output += data.toString();
});

child.stderr.on('data', (data) => {
    errorOutput += data.toString();
});

// Send a simple MCP initialization message
setTimeout(() => {
    const initMessage = {
        jsonrpc: "2.0",
        id: 1,
        method: "initialize",
        params: {
            protocolVersion: "2024-11-05",
            capabilities: {},
            clientInfo: {
                name: "test-client",
                version: "1.0.0"
            }
        }
    };
    
    child.stdin.write(JSON.stringify(initMessage) + '\n');
    
    // Give it a moment to respond, then kill
    setTimeout(() => {
        child.kill('SIGTERM');
    }, 2000);
}, 1000);

child.on('close', (code) => {
    console.log('\n=== Test Results ===');
    console.log(`Exit code: ${code}`);
    console.log('\nStdout:', output);
    console.log('\nStderr:', errorOutput);
    
    if (errorOutput.includes('Socket MCP server started successfully') || 
        output.includes('result') || 
        code === 0 || 
        code === null) {
        console.log('\n✅ Socket MCP server appears to be working!');
    } else {
        console.log('\n❌ Socket MCP server may have issues');
    }
});

child.on('error', (err) => {
    console.error('Failed to start process:', err);
});
