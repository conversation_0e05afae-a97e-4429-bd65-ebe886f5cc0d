# Socket.dev MCP Server - VS Code Setup

This guide shows how to set up the Socket.dev MCP server with VS Code.

## Prerequisites

1. **Node.js** installed on your system
2. **Socket.dev API Key** - Get one from [Socket.dev API documentation](https://docs.socket.dev/reference/creating-and-managing-api-tokens)
3. **VS Code** with MCP support

## Setup Steps

### 1. Verify the Build

The Socket MCP server should already be built. You can verify by checking:

```bash
ls socket-mcp/build/index.js
```

If the file doesn't exist, build it:

```bash
cd socket-mcp
npm install
npm run build
```

### 2. VS Code Configuration

The `.vscode/mcp.json` file has been created with two server configurations:

#### Option A: Direct Node.js execution (Recommended)
```json
"socket-mcp-direct": {
    "type": "stdio",
    "command": "node",
    "args": ["./socket-mcp/build/index.js"],
    "env": {
        "SOCKET_API_KEY": "${input:socket_api_key}"
    }
}
```

#### Option B: Using npx
```json
"socket-mcp-npx": {
    "type": "stdio",
    "command": "npx",
    "args": ["--yes", "--package", "./socket-mcp", "socket-mcp"],
    "env": {
        "SOCKET_API_KEY": "${input:socket_api_key}"
    }
}
```

### 3. Using the Socket MCP Server

1. **Open VS Code** in this directory
2. **Access MCP settings** (this depends on your VS Code MCP extension)
3. **Select** either `socket-mcp-direct` or `socket-mcp-npx`
4. **Enter your Socket.dev API key** when prompted
5. **Start using** the `depscore` tool

### 4. Testing the Tool

Once configured, you can ask your AI assistant questions like:

- "Check the security score for express version 4.18.2"
- "What's the dependency score for lodash?"
- "Scan the security of react@18.0.0"

## Available Tool

### `depscore`

**Parameters:**
- `ecosystem`: Package ecosystem (npm, pypi, etc.) - defaults to "npm"
- `depname`: Name of the dependency
- `version`: Version of the dependency - defaults to "unknown"

**Example usage:**
```
Check dependency score for express version 4.18.2 in npm ecosystem
```

## Troubleshooting

### Common Issues

1. **"SOCKET_API_KEY environment variable is not set"**
   - Make sure you entered a valid API key when prompted
   - Verify the API key is correct

2. **"Package not found"**
   - Check the package name spelling
   - Verify the ecosystem (npm, pypi, etc.)
   - Some packages might not be in Socket.dev's database

3. **Node.js not found**
   - Ensure Node.js is installed and in your PATH
   - Try using the direct node command instead of npx

### Testing the Server

You can test the server manually:

```bash
node test-socket-mcp.js
```

This will verify the server can start and respond to basic MCP messages.

## Security Notes

- Your Socket.dev API key is stored securely in VS Code's input system
- The API key is passed as an environment variable to the MCP server
- Logs are written to `socket-mcp.log` and `socket-mcp-error.log` files

## Next Steps

- Configure your AI assistant to use the Socket MCP server
- Set up automated dependency scanning in your development workflow
- Explore Socket.dev's other security features
